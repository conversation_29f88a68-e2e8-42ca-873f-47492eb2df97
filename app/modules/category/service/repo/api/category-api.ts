import { HttpBody } from "@effect/platform";
import { Effect, Layer, Schema } from "effect";
import { ApiHttpClient } from "~/core/service/repo/api";
import { handleDResponse, handleResponse } from "~/core/service/repo/api/utils";
import type { CategoryCreate, CategoryUpdate } from "../../model/category";
import { CategoryRepository } from "../../model/repository";
import {
	CategoryFromApi,
	CategoryListFromApi,
	CategoryWithSubcategoriesFromApi,
	CreateCategoryApiFromCreateCategory,
	CreateCategoryApiResponse,
	UpdateCategoryApiFromUpdateCategory,
} from "./dto";

const baseUrl = "/v1/categories";

const makeCategoryApiRepo = ApiHttpClient.pipe(
	Effect.andThen(({ httpClient }) => httpClient),
	Effect.andThen((httpClient) => ({
		getAll: () =>
			httpClient
				.get(baseUrl)
				.pipe(Effect.flatMap(handleDResponse(CategoryListFromApi))),
		getById: (id: string) =>
			httpClient
				.get(`${baseUrl}/${id}`)
				.pipe(Effect.flatMap(handleDResponse(CategoryFromApi))),
		create: (category: CategoryCreate) =>
			httpClient
				.post(baseUrl, {
					body: HttpBody.unsafeJson(
						Schema.decodeUnknownSync(CreateCategoryApiFromCreateCategory)(
							category,
						),
					),
				})
				.pipe(Effect.flatMap(handleDResponse(CreateCategoryApiResponse))),
		update: (category: CategoryUpdate) =>
			httpClient
				.put(baseUrl, {
					body: HttpBody.unsafeJson(
						Schema.decodeUnknownSync(UpdateCategoryApiFromUpdateCategory)(
							category,
						),
					),
				})
				.pipe(Effect.flatMap(handleResponse)),
		delete: (id: string) =>
			httpClient.del(`${baseUrl}/${id}`).pipe(Effect.flatMap(handleResponse)),
		getSubcategories: (id: string) =>
			httpClient
				.get(`${baseUrl}/${id}/subcategories`)
				.pipe(Effect.flatMap(handleDResponse(CategoryListFromApi))),
		getDetails: (id: string) =>
			httpClient
				.get(`${baseUrl}/${id}/details`)
				.pipe(
					Effect.flatMap(handleDResponse(CategoryWithSubcategoriesFromApi)),
				),
		getParents: () =>
			httpClient
				.get(`${baseUrl}/parents`)
				.pipe(Effect.flatMap(handleDResponse(CategoryListFromApi))),
	})),
	Effect.provide(ApiHttpClient.Live),
);

export const categoryApiRepoLive = Layer.effect(
	CategoryRepository,
	makeCategoryApiRepo,
);
