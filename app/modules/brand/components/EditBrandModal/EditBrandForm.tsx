import { Tag, Hash } from "lucide-react";
import CloseModal from "~/core/components/CloseModal";
import { cn } from "~/core/utils/classes";
import useEditBrandModal, {
	type EditBrandModalProps,
} from "./use-edit-brand-modal";

export default function EditBrandForm({
	isOpen,
	setIsOpen,
	brand,
}: EditBrandModalProps) {
	const { form, handleClose } = useEditBrandModal({
		isOpen,
		setIsOpen,
		brand,
	});

	return (
		<div className={cn("modal", isOpen && "modal-open")}>
			<div className="modal-box">
				<CloseModal onClose={handleClose} />
				<h3 className="font-bold text-lg">Editar Marca</h3>
				<form
					onSubmit={(e) => {
						e.preventDefault();
						form.handleSubmit();
					}}
				>
					<form.AppForm>
						<fieldset className="fieldset">
							<form.AppField
								name="name"
								children={({ FSTextField }) => (
									<FSTextField
										label="Nombre"
										placeholder="Nombre de la marca"
										prefixComponent={<Tag size={16} />}
									/>
								)}
							/>
							<form.AppField
								name="code"
								children={({ FSTextField }) => (
									<FSTextField
										label="Código"
										placeholder="Código de la marca"
										prefixComponent={<Hash size={16} />}
									/>
								)}
							/>
						</fieldset>
						<div className="modal-action">
							<button type="submit" className="btn btn-primary">
								Actualizar
							</button>
						</div>
					</form.AppForm>
				</form>
			</div>
		</div>
	);
}
