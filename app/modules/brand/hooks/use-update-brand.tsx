import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "~/config/context/serviceProvider";
import { AppRuntime } from "~/core/service/utils/runtimes";
import type { Brand, UpdateBrand } from "../service/model/brand";
import { brandOptions } from "./brand-options";

export default function useUpdateBrand() {
	const service = useService();
	const { brand } = service;
	const queryClient = useQueryClient();
	const queryKey = brandOptions(service).queryKey;

	return useMutation({
		mutationFn: (updateBrand: UpdateBrand) =>
			AppRuntime.runPromise(brand.update(updateBrand)),
		onSuccess: (_, updateBrand) => {
			queryClient.setQueryData(queryKey, (old: Brand[] | undefined) =>
				create(old ?? [], (draft) => {
					const index = draft.findIndex((b) => b.id === updateBrand.id);
					if (index !== -1) {
						draft[index] = {
							...draft[index],
							name: updateBrand.name,
							code: updateBrand.code,
							updatedAt: new Date().toISOString(),
						};
					}
				}),
			);
		},
	});
}
