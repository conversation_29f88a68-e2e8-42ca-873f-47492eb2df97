import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "~/config/context/serviceProvider";
import { AppRuntime } from "~/core/service/utils/runtimes";
import type { Category, CategoryUpdate } from "../service/model/category";
import { categoryOptions } from "./category-options";

export default function useUpdateCategory() {
	const service = useService();
	const { category } = service;
	const queryClient = useQueryClient();
	const queryKey = categoryOptions(service).queryKey;

	return useMutation({
		mutationFn: (updateCategory: CategoryUpdate) =>
			AppRuntime.runPromise(category.update(updateCategory)),
		onSuccess: (_, updateCategory) => {
			queryClient.setQueryData(queryKey, (old: Category[] | undefined) =>
				create(old ?? [], (draft) => {
					const index = draft.findIndex((c) => c.id === updateCategory.id);
					if (index !== -1) {
						draft[index] = {
							...draft[index],
							name: updateCategory.name,
							code: updateCategory.code,
							categoryId: updateCategory.categoryId || null,
							updatedAt: new Date().toISOString(),
						};
					}
				}),
			);
		},
	});
}
