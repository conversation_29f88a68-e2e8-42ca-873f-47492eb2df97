import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "~/config/context/serviceProvider";
import { AppRuntime } from "~/core/service/utils/runtimes";
import type { Brand } from "../service/model/brand";
import { brandOptions } from "./brand-options";

export default function useDeleteBrand() {
	const service = useService();
	const { brand } = service;
	const queryClient = useQueryClient();
	const queryKey = brandOptions(service).queryKey;

	return useMutation({
		mutationFn: (id: string) => AppRuntime.runPromise(brand.delete(id)),
		onSuccess: (_, id) => {
			queryClient.setQueryData(queryKey, (old: Brand[] | undefined) =>
				create(old ?? [], (draft) => {
					const index = draft.findIndex((b) => b.id === id);
					if (index !== -1) {
						draft.splice(index, 1);
					}
				}),
			);
		},
	});
}
