import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "~/config/context/serviceProvider";
import { AppRuntime } from "~/core/service/utils/runtimes";
import type { Category, CategoryCreate } from "../service/model/category";
import { categorySubcategoriesOptions } from "./category-options";
import useSelectedCategory from "./use-selected-category";

export default function useCreateCategory() {
	const service = useService();
	const { category } = service;
	const queryClient = useQueryClient();

	const { categoryId } = useSelectedCategory();

	const queryKey = categorySubcategoriesOptions(
		service,
		categoryId || "",
	).queryKey;

	return useMutation({
		mutationFn: (newCategory: CategoryCreate) =>
			AppRuntime.runPromise(category.create(newCategory)),
		onSuccess: (id, newCategory) => {
			queryClient.setQueryData(queryKey, (old: Category[] | undefined) =>
				create(old ?? [], (draft) => {
					draft.push({
						id,
						name: newCategory.name,
						code: newCategory.code,
						categoryId: newCategory.categoryId || null,
						createdAt: new Date().toISOString(),
						updatedAt: null,
						deletedAt: null,
					} as Category);
				}),
			);
		},
	});
}
