import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "~/config/context/serviceProvider";
import { AppRuntime } from "~/core/service/utils/runtimes";
import type { Brand, CreateBrand } from "../service/model/brand";
import { brandOptions } from "./brand-options";

export default function useCreateBrand() {
	const service = useService();
	const { brand } = service;
	const queryClient = useQueryClient();
	const queryKey = brandOptions(service).queryKey;

	return useMutation({
		mutationFn: (newBrand: CreateBrand) =>
			AppRuntime.runPromise(brand.create(newBrand)),
		onSuccess: (id, newBrand) => {
			queryClient.setQueryData(queryKey, (old: Brand[] | undefined) =>
				create(old ?? [], (draft) => {
					draft.push({
						id,
						name: newBrand.name,
						code: newBrand.code,
						createdAt: new Date().toISOString(),
						updatedAt: null,
						deletedAt: null,
					} as Brand);
				}),
			);
		},
	});
}
